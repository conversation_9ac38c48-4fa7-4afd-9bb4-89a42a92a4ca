import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import http from "http";
import connectDB from "./db.js";
import { authRouter } from "./auth/route.js";
import { gameRouter } from "./game/route.js";
import { initIo } from "./sockets/socket.js";

dotenv.config();

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 4000;

app.use(express.json());
app.use(cors({ origin: "http://localhost:5173", credentials: true }));

app.use("/api/auth", authRouter);
app.use("/api/games", gameRouter);

connectDB();
initIo(server);

server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
