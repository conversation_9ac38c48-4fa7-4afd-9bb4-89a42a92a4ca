import { useEffect, useState } from "react";
import API from "../components/api/api";
import socket from "../sockets/socket";

const difficulties = {
  easy: 8,
  medium: 16,
  hard: 32,
};
const gridColumns = {
  easy: 4,
  medium: 4,
  hard: 8,
};

const emojis = [
  "🍎",
  "🍌",
  "🍒",
  "🍇",
  "🍉",
  "🍓",
  "🍑",
  "🍍",
  "🥥",
  "🥝",
  "🥑",
  "🍆",
  "🥔",
  "🥕",
  "🌽",
  "🌶️",
  "🥒",
  "🥬",
  "🥦",
  "🧄",
  "🧅",
  "🍄",
  "🥜",
  "🌰",
  "🍞",
  "🥐",
  "🥖",
  "🥨",
  "🥯",
  "🥞",
  "🧇",
  "🧀",
];

const GamePage = () => {
  const [mode, setMode] = useState("");
  const [cards, setCards] = useState([]);
  const [flipped, setFlipped] = useState([]);
  const [matched, setMatched] = useState([]);
  const [moves, setMoves] = useState(0);
  const [time, setTime] = useState(0);
  const [timerActive, setTimerActive] = useState(false);
  const [leaderboard, setLeaderboard] = useState([]);

  const [battleMode, setBattleMode] = useState(false);
  const [roomInfo, setRoomInfo] = useState(null);
  const [opponents, setOpponents] = useState([]);
  const [opponentFlips, setOpponentFlips] = useState([]);
  const [onlineCount, setOnlineCount] = useState(0);
  const [battleStatus, setBattleStatus] = useState("waiting");
  const [opponentActions, setOpponentActions] = useState([]);
  const [gameEnded, setGameEnded] = useState(false);
  const [winner, setWinner] = useState(null);

  // Hints system
  const [hintsRemaining, setHintsRemaining] = useState(3);
  const [hintCards, setHintCards] = useState([]);
  const [isHintActive, setIsHintActive] = useState(false);

  useEffect(() => {
    let timer;
    if (timerActive) {
      timer = setInterval(() => setTime((t) => t + 1), 1000);
    }
    return () => clearInterval(timer);
  }, [timerActive]);

  useEffect(() => {
    socket.on("gameSaved", (data) => console.log("Game saved:", data));
    socket.on("leaderboard", (data) => setLeaderboard(data.leaderboard));
    socket.on("leaderboardUpdate", (data) => {
      if (data.mode === mode) {
        setLeaderboard(data.leaderboard);
      }
    });

    socket.on("battleJoined", (data) => {
      setRoomInfo(data);
      setOpponents(data.players.filter((p) => p.id !== socket.id));
      setBattleStatus("playing");
      console.log("Joined battle room:", data);
    });

    socket.on("roomUpdate", (data) => {
      setRoomInfo(data);
      setOpponents(data.players.filter((p) => p.id !== socket.id));
    });

    socket.on("onlinePlayersCount", (count) => {
      setOnlineCount(count);
    });

    socket.on("opponentFlip", (data) => {
      setOpponentFlips((prev) => [...prev, data.card]);
      setOpponentActions((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "flip",
          playerName: data.playerName,
          card: data.card,
          timestamp: Date.now(),
        },
      ]);

      setTimeout(() => {
        setOpponentFlips((prev) =>
          prev.filter((card) => card.id !== data.card.id)
        );
      }, 800);

      setTimeout(() => {
        setOpponentActions((prev) =>
          prev.filter((action) => action.id !== Date.now())
        );
      }, 3000);
    });

    socket.on("opponentMatch", (data) => {
      setOpponentActions((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "match",
          playerName: data.playerName,
          value: data.value,
          timestamp: Date.now(),
        },
      ]);

      setTimeout(() => {
        setOpponentActions((prev) =>
          prev.filter((action) => action.id !== Date.now())
        );
      }, 3000);
    });

    socket.on("opponentGameCompleted", (data) => {
      setOpponentActions((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "completed",
          playerName: data.playerName,
          score: data.score,
          timestamp: Date.now(),
        },
      ]);

      setTimeout(() => {
        setOpponentActions((prev) =>
          prev.filter((action) => action.id !== Date.now())
        );
      }, 5000);
    });

    // Handle game end (when someone wins the battle)
    socket.on("gameEnded", (data) => {
      setGameEnded(true);
      setWinner(data.winner);
      setTimerActive(false); // Stop timer for everyone

      setOpponentActions((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "gameEnded",
          message: data.message,
          winner: data.winner,
          timestamp: Date.now(),
        },
      ]);

      console.log("Battle ended:", data);
    });

    socket.on("roomReset", (data) => {
      setGameEnded(false);
      setWinner(null);
      setOpponentActions([]);
      console.log("Room reset:", data.message);
    });

    return () => {
      socket.off("gameSaved");
      socket.off("leaderboard");
      socket.off("leaderboardUpdate");
      socket.off("battleJoined");
      socket.off("roomUpdate");
      socket.off("onlinePlayersCount");
      socket.off("opponentFlip");
      socket.off("opponentMatch");
      socket.off("opponentGameCompleted");
      socket.off("gameEnded");
      socket.off("roomReset");
    };
  }, [mode]);

  const generateCards = (difficulty) => {
    const size = difficulties[difficulty];
    const selectedEmojis = emojis.slice(0, size / 2);
    const pair = [...selectedEmojis, ...selectedEmojis];
    const shuffled = pair.sort(() => Math.random() - 0.5);
    return shuffled.map((value, index) => ({ id: index, value }));
  };

  const startGame = () => {
    const newCards = generateCards(mode);
    setCards(newCards);
    setFlipped([]);
    setMatched([]);
    setMoves(0);
    setTime(0);
    setTimerActive(true);
    setOpponentFlips([]);
    setOpponentActions([]);

    // Reset hints
    setHintsRemaining(3);
    setHintCards([]);
    setIsHintActive(false);

    socket.emit("joinGame", { mode, cards: newCards });
  };

  const startBattle = () => {
    if (!mode) return;

    const userData = JSON.parse(localStorage.getItem("userdata"));
    if (!userData) {
      alert("Please login to join battles!");
      return;
    }

    setBattleStatus("searching");
    setBattleMode(true);

    socket.emit("findBattle", {
      mode,
      playerName: userData.user.name,
      userId: userData.user.id,
    });

    // Start the game immediately
    startGame();
  };

  const resetRoom = () => {
    socket.emit("resetRoom");
    setGameEnded(false);
    setWinner(null);
    setOpponentActions([]);
  };

  const useHint = () => {
    if (hintsRemaining <= 0 || isHintActive || cards.length === 0) return;

    // Find unmatched cards
    const unmatchedCards = cards.filter(
      (card) => !matched.includes(card.value)
    );

    // Find a pair of matching cards that aren't already matched
    const availableValues = [
      ...new Set(unmatchedCards.map((card) => card.value)),
    ];

    if (availableValues.length === 0) return; // No pairs left

    // Pick a random value from available unmatched values
    const randomValue =
      availableValues[Math.floor(Math.random() * availableValues.length)];

    // Find both cards with this value
    const matchingPair = unmatchedCards.filter(
      (card) => card.value === randomValue
    );

    if (matchingPair.length === 2) {
      setHintCards(matchingPair);
      setIsHintActive(true);
      setHintsRemaining((prev) => prev - 1);

      // Hide hint after 2 seconds
      setTimeout(() => {
        setHintCards([]);
        setIsHintActive(false);
      }, 2000);
    }
  };

  const handleFlip = (card) => {
    if (battleMode && gameEnded) return;
    if (flipped.length === 2 || flipped.some((f) => f.id === card.id)) return;

    const newFlipped = [...flipped, card];
    setFlipped(newFlipped);
    socket.emit("flipCard", card); // tell server we flipped

    if (newFlipped.length === 2) {
      setMoves((m) => m + 1);
      if (newFlipped[0].value === newFlipped[1].value) {
        setMatched((m) => [...m, newFlipped[0].value]);
        socket.emit("matchFound", newFlipped[0].value);
      }
      setTimeout(() => setFlipped([]), 800);
    }
  };

  const isGameOver = matched.length === difficulties[mode] / 2;

  useEffect(() => {
    if (isGameOver) {
      setTimerActive(false);
      saveGame();
    }
  }, [isGameOver]);

  const saveGame = async () => {
    const score = Math.max(0, 1000 - (moves * 10 + time));
    try {
      await API.post("/games", { mode, moves, time, score });
      socket.emit("requestLeaderboard", { mode });

      // Emit game completion for battle mode
      if (battleMode) {
        socket.emit("gameCompleted", { mode, moves, time, score });
      }
    } catch (error) {
      console.error("Error saving game", error.response?.data || error.message);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-red-900 via-black to-red-700 p-4 font-['Bangers']">
      <h1 className="text-6xl font-extrabold text-red-500 mb-8 tracking-wider uppercase">
        Memory Card Clash
      </h1>

      <div className="mb-4 text-green-400 text-lg">
        {onlineCount} Players Online
      </div>

      <div className="flex gap-4 mb-8">
        <select
          value={mode}
          onChange={(e) => setMode(e.target.value)}
          className="border-4 border-red-400 rounded-lg px-4 py-2 bg-black text-red-300"
        >
          <option value="">Select Difficulty</option>
          <option value="easy">Easy (4x2)</option>
          <option value="medium">Medium (4x4)</option>
          <option value="hard">Hard (8x4)</option>
        </select>
        <button
          onClick={startGame}
          className="bg-red-600 text-white px-6 py-2 rounded-lg border-4 border-red-400"
          disabled={!mode}
        >
          Solo Practice
        </button>
        <button
          onClick={startBattle}
          className="bg-orange-600 text-white px-6 py-2 rounded-lg border-4 border-orange-400 animate-pulse"
          disabled={!mode || battleStatus === "searching"}
        >
          {battleStatus === "searching" ? "Finding Battle..." : "Join Battle"}
        </button>
      </div>

      {battleMode && (
        <div className="mb-4 p-4 bg-black rounded-lg border-2 border-orange-400">
          <div className="text-orange-400 text-lg mb-2">
            ⚔️ Battle Mode - {roomInfo?.mode?.toUpperCase()}
          </div>
          <div className="text-sm text-gray-300">
            Players in room: {roomInfo?.playersCount || 0}/
            {roomInfo?.maxPlayers || 4}
          </div>
          {opponents.length > 0 && (
            <div className="text-sm text-blue-300 mt-1">
              Opponents: {opponents.map((p) => p.name).join(", ")}
            </div>
          )}
        </div>
      )}

      {opponentActions.length > 0 && (
        <div className="mb-4 w-full max-w-md">
          <div className="bg-black p-3 rounded-lg border-2 border-blue-400 max-h-32 overflow-y-auto">
            <div className="text-blue-400 text-sm font-bold mb-2">
              🎯 Live Battle Feed
            </div>
            {opponentActions.slice(-3).map((action) => (
              <div
                key={action.id}
                className={`text-xs mb-1 ${
                  action.type === "gameEnded"
                    ? "text-yellow-400 font-bold"
                    : "text-gray-300"
                }`}
              >
                {action.type === "flip" &&
                  `${action.playerName} flipped a card`}
                {action.type === "match" &&
                  `${action.playerName} found a match! ${action.value}`}
                {action.type === "completed" &&
                  `${action.playerName} completed the game! Score: ${action.score}`}
                {action.type === "gameEnded" && `🏆 ${action.message}`}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex gap-6 mb-8 text-lg text-red-300">
        <p className="bg-black px-4 py-2 rounded-lg">Moves: {moves}</p>
        <p className="bg-black px-4 py-2 rounded-lg">Time: {time}s</p>
        <div className="flex items-center gap-2">
          <button
            onClick={useHint}
            disabled={
              hintsRemaining <= 0 ||
              isHintActive ||
              !cards.length ||
              (battleMode && gameEnded)
            }
            className={`px-4 py-2 rounded-lg border-2 text-sm font-bold ${
              hintsRemaining > 0 &&
              !isHintActive &&
              cards.length > 0 &&
              !(battleMode && gameEnded)
                ? "bg-yellow-600 border-yellow-400 text-white hover:bg-yellow-700 cursor-pointer"
                : "bg-gray-600 border-gray-500 text-gray-400 cursor-not-allowed"
            }`}
          >
            💡 Hint ({hintsRemaining})
          </button>
        </div>
      </div>

      <div
        className="grid gap-2"
        style={{
          gridTemplateColumns: `repeat(${gridColumns[mode] || 4}, 80px)`,
        }}
      >
        {cards.map((card) => {
          const isFlipped =
            flipped.some((f) => f.id === card.id) ||
            matched.includes(card.value);
          const isOpponentFlipped = opponentFlips.some((f) => f.id === card.id);
          const isHintCard = hintCards.some((f) => f.id === card.id);

          return (
            <div
              key={card.id}
              onClick={() => handleFlip(card)}
              className={`w-20 h-20 flex items-center justify-center rounded-lg text-4xl cursor-pointer relative ${
                isFlipped
                  ? "bg-red-600"
                  : isHintCard
                  ? "bg-yellow-500 animate-bounce"
                  : isOpponentFlipped
                  ? "bg-blue-500 animate-pulse"
                  : "bg-black"
              } ${isOpponentFlipped ? "ring-2 ring-blue-300" : ""} ${
                isHintCard
                  ? "ring-4 ring-yellow-300 shadow-lg shadow-yellow-400"
                  : ""
              }`}
            >
              {isFlipped
                ? card.value
                : isHintCard
                ? card.value
                : isOpponentFlipped
                ? "👁️"
                : "?"}
              {isOpponentFlipped && !isHintCard && (
                <div className="absolute -top-2 -right-2 text-xs bg-blue-500 text-white rounded-full w-4 h-4 flex items-center justify-center">
                  !
                </div>
              )}
              {isHintCard && (
                <div className="absolute -top-2 -right-2 text-xs bg-yellow-500 text-black rounded-full w-4 h-4 flex items-center justify-center font-bold">
                  💡
                </div>
              )}
            </div>
          );
        })}
      </div>

      {isGameOver && !battleMode && (
        <div className="mt-8 text-2xl text-red-300">
          Victory! Score: {Math.max(0, 1000 - (moves * 10 + time))}
        </div>
      )}

      {/* Battle Winner Display */}
      {battleMode && gameEnded && winner && (
        <div className="mt-8 p-6 bg-gradient-to-r from-yellow-600 to-orange-600 rounded-lg border-4 border-yellow-400 text-center">
          <div className="text-3xl font-bold text-white mb-2">
            🏆 BATTLE WINNER! 🏆
          </div>
          <div className="text-xl text-yellow-100 mb-2">{winner.name}</div>
          <div className="text-sm text-yellow-200 mb-4">
            Score: {winner.score} • Time: {winner.time}s • Moves: {winner.moves}
          </div>
          <button
            onClick={resetRoom}
            className="bg-green-600 text-white px-6 py-2 rounded-lg border-2 border-green-400 hover:bg-green-700"
          >
            🔄 New Battle
          </button>
        </div>
      )}

      {battleMode && isGameOver && !gameEnded && (
        <div className="mt-8 text-2xl text-green-400 animate-pulse">
          🎉 You Won the Battle! Score:{" "}
          {Math.max(0, 1000 - (moves * 10 + time))}
        </div>
      )}

      {leaderboard.length > 0 && (
        <div className="mt-10 w-full max-w-md bg-black p-4 rounded-lg text-red-300 border-2 border-yellow-400">
          <h2 className="text-xl font-bold mb-4 flex items-center">
            🏆 Live Leaderboard
            <span className="ml-2 text-xs bg-green-500 text-white px-2 py-1 rounded animate-pulse">
              LIVE
            </span>
          </h2>
          <div className="text-xs text-gray-400 mb-3">
            {mode ? `${mode.toUpperCase()} Mode` : "All Modes"} • Updates in
            realtime
          </div>
          {leaderboard.map((entry, idx) => (
            <div
              key={idx}
              className="flex justify-between items-center py-1 border-b border-gray-700 last:border-b-0"
            >
              <span className="flex items-center">
                <span
                  className={`mr-2 ${
                    idx === 0
                      ? "text-yellow-400"
                      : idx === 1
                      ? "text-gray-300"
                      : idx === 2
                      ? "text-orange-400"
                      : "text-red-300"
                  }`}
                >
                  {idx + 1}.
                </span>
                {entry.user.name}
              </span>
              <span className="font-bold text-green-400">
                {entry.score} pts
              </span>
            </div>
          ))}
        </div>
      )}

      {!battleMode && (
        <div className="mt-6 text-center text-sm text-gray-400 max-w-md">
          <p>Join a battle to compete with other players in real-time!</p>
          <p>See their card flips and race to complete the game first.</p>
        </div>
      )}
    </div>
  );
};

export default GamePage;
