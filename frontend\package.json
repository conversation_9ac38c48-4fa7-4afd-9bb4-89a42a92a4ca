{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-query": "^5.85.5", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "react-router-dom": "^7.8.2", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.12", "zod": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}