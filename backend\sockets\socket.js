import { Server } from "socket.io";

let io;

export const initIo = (server) => {
  io = new Server(server, {
    cors: {
      origin: "http://localhost:5173",
      methods: ["GET", "POST"],
      credentials: true,
    },
  });

  io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);

    socket.on("joinRoom", (roomId) => {
      socket.join(roomId);
      console.log(`Player joined room: ${roomId}`);
      io.to(roomId).emit("playerJoined", { id: socket.id });
    });

    socket.on("flipCard", ({ roomId, card }) => {
      socket.to(roomId).emit("opponentFlipped", card);
      console.log(`Card flipped in room ${roomId}:`, card);
    });

    socket.on("disconnect", () => {
      console.log("Client disconnected:", socket.id);
    });
  });

  return io;
};

export const getIo = () => {
  if (!io) throw new Error("Socket.io not initialized");
  return io;
};
