import { Server } from "socket.io";

let io;

// Room management
const gameRooms = new Map(); // roomId -> { players: [], mode: string, gameState: {}, maxPlayers: 4 }
const playerRooms = new Map(); // socketId -> roomId
const waitingPlayers = new Map(); // mode -> [socketId]

export const initIo = (server) => {
  io = new Server(server, {
    cors: {
      origin: "http://localhost:5173",
      methods: ["GET", "POST"],
      credentials: true,
    },
  });

  io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);

    // Auto-join room based on difficulty
    socket.on("findBattle", ({ mode, playerName, userId }) => {
      console.log(`Player ${playerName} looking for ${mode} battle`);

      socket.playerName = playerName;
      socket.userId = userId;
      socket.mode = mode;

      // Try to find existing room with space
      let roomId = findAvailableRoom(mode);

      if (!roomId) {
        // Create new room
        roomId = `${mode}_${Date.now()}_${Math.random()
          .toString(36)
          .substr(2, 9)}`;
        gameRooms.set(roomId, {
          players: [],
          mode,
          gameState: { started: false, cards: null },
          maxPlayers: 4,
          createdAt: Date.now(),
        });
      }

      joinRoom(socket, roomId);
    });

    // Handle card flips in battle
    socket.on("flipCard", (card) => {
      const roomId = playerRooms.get(socket.id);
      if (roomId) {
        socket.to(roomId).emit("opponentFlip", {
          card,
          playerName: socket.playerName,
          playerId: socket.id,
        });
        console.log(
          `${socket.playerName} flipped card in room ${roomId}:`,
          card
        );
      }
    });

    // Handle matches found
    socket.on("matchFound", (value) => {
      const roomId = playerRooms.get(socket.id);
      if (roomId) {
        socket.to(roomId).emit("opponentMatch", {
          value,
          playerName: socket.playerName,
          playerId: socket.id,
        });
      }
    });

    // Handle game completion
    socket.on("gameCompleted", (gameData) => {
      const roomId = playerRooms.get(socket.id);
      if (roomId) {
        socket.to(roomId).emit("opponentGameCompleted", {
          ...gameData,
          playerName: socket.playerName,
          playerId: socket.id,
        });

        // Broadcast updated leaderboard to all clients
        io.emit("requestLeaderboardUpdate", { mode: socket.mode });
      }
    });

    // Handle leaderboard requests
    socket.on("requestLeaderboard", ({ mode }) => {
      // This will be handled by the game controller
      socket.emit("leaderboardRequested", { mode });
    });

    // Handle disconnection
    socket.on("disconnect", () => {
      console.log("Client disconnected:", socket.id);
      handlePlayerDisconnect(socket);
    });

    // Send initial online players count
    socket.emit("onlinePlayersCount", io.engine.clientsCount);
    socket.broadcast.emit("onlinePlayersCount", io.engine.clientsCount);
  });

  return io;
};

// Helper functions
function findAvailableRoom(mode) {
  for (const [roomId, room] of gameRooms.entries()) {
    if (
      room.mode === mode &&
      room.players.length < room.maxPlayers &&
      !room.gameState.started
    ) {
      return roomId;
    }
  }
  return null;
}

function joinRoom(socket, roomId) {
  const room = gameRooms.get(roomId);
  if (!room) return;

  // Leave previous room if any
  const previousRoom = playerRooms.get(socket.id);
  if (previousRoom) {
    leaveRoom(socket, previousRoom);
  }

  // Join new room
  socket.join(roomId);
  playerRooms.set(socket.id, roomId);

  const playerData = {
    id: socket.id,
    name: socket.playerName,
    userId: socket.userId,
    joinedAt: Date.now(),
  };

  room.players.push(playerData);

  // Notify all players in room
  io.to(roomId).emit("roomUpdate", {
    roomId,
    players: room.players,
    playersCount: room.players.length,
    maxPlayers: room.maxPlayers,
    mode: room.mode,
  });

  // Notify player they joined
  socket.emit("battleJoined", {
    roomId,
    players: room.players,
    mode: room.mode,
  });

  console.log(
    `Player ${socket.playerName} joined room ${roomId}. Players: ${room.players.length}/${room.maxPlayers}`
  );
}

function leaveRoom(socket, roomId) {
  const room = gameRooms.get(roomId);
  if (!room) return;

  room.players = room.players.filter((p) => p.id !== socket.id);
  socket.leave(roomId);
  playerRooms.delete(socket.id);

  if (room.players.length === 0) {
    // Delete empty room
    gameRooms.delete(roomId);
    console.log(`Room ${roomId} deleted - no players left`);
  } else {
    // Notify remaining players
    io.to(roomId).emit("roomUpdate", {
      roomId,
      players: room.players,
      playersCount: room.players.length,
      maxPlayers: room.maxPlayers,
      mode: room.mode,
    });
  }
}

function handlePlayerDisconnect(socket) {
  const roomId = playerRooms.get(socket.id);
  if (roomId) {
    leaveRoom(socket, roomId);
  }

  // Update online count
  socket.broadcast.emit("onlinePlayersCount", io.engine.clientsCount - 1);
}

export const getIo = () => {
  if (!io) throw new Error("Socket.io not initialized");
  return io;
};
